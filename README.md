# :muscle: RackUp

<!-- markdownlint-disable link-image-style -->

- [Prerequisites](#prerequisites)
- [Documentation](#documentation)
- [Packages](#packages)
- [Authoring](#authoring)

<!-- markdownlint-enable link-image-style -->

## Prerequisites

- [Docker ↗][docker]
- [Node.js ↗][node-js] (via [Fast Node Manager ↗][fast-node-manager])
- [pnpm ↗][pnpm]

## Documentation

- [DevOps ↗][devops]

## Packages

- [API ↗][api]
- [Mobile ↗][mobile]

## Authoring

- [Documentation ↗][documentation]

[api]: packages/api/README.md
[devops]: documentation/DevOps/README.md
[docker]: https://www.docker.com
[documentation]: documentation/README.md
[fast-node-manager]: https://github.com/Schniz/fnm
[mobile]: packages/mobile/README.md
[node-js]: https://nodejs.org
[pnpm]: https://pnpm.io
