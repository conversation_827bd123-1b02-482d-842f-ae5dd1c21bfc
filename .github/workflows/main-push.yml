name: Main Push
on:
  push:
    branches:
      - main
jobs:
  deploy-api:
    name: Deploy API
    needs: [package-checks, repository-checks]
    runs-on: ubuntu-latest
    environment: Development
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/setup
        with:
          package: "@rackup/api"
      - name: Deploy
        env:
          CONVEX_DEPLOY_KEY: ${{ secrets.CONVEX_DEPLOY_KEY }}
        run: pnpm --filter @rackup/api exec convex deploy
      - name: Seed
        env:
          CONVEX_DEPLOY_KEY: ${{ secrets.CONVEX_DEPLOY_KEY }}
        run: pnpm --filter @rackup/api run seed
  package-checks:
    name: Package Checks
    runs-on: ubuntu-latest
    strategy:
      matrix:
        packages: ["@rackup/api", "@rackup/mobile"]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/setup
        with:
          package: ${{ matrix.packages }}
      - name: Checks
        uses: ./.github/actions/package-checks
        with:
          package: ${{ matrix.packages }}
  repository-checks:
    name: Repository Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/setup
        with:
          package: "."
      - name: Checks
        uses: ./.github/actions/repository-checks
