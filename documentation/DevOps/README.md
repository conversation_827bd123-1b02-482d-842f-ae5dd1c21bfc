# :infinity: DevOps

[⏎ Home][home]

<!-- markdownlint-disable link-image-style -->

- [Local](#local)
  - [Getting Started](#getting-started)
    - [1. Start API](#1-start-api)
- [Cloud](#cloud)
  - [Configuring a Environment](#configuring-a-environment)
    - [1. Set Convex Environment Variables](#1-set-convex-environment-variables)
    - [2. Generate Convex Deployment Key](#2-generate-convex-deployment-key)
    - [3. Set GitHub Environment Variables](#3-set-github-environment-variables)

<!-- markdownlint-enable link-image-style -->

## Local

### Getting Started

#### 1. Start API

```sh
cd packages/api

# Copy example environment variables
cp .env.example .env.local

pnpm start
```

In a separate terminal

```sh
cd packages/api

# Seed
pnpm run seed
```

## Cloud

### Configuring a Environment

#### 1. Set Convex Environment Variables

1. Navigate to the [Convex Dashboard][convex-dashboard]
2. Select project
3. Navigate to **Settings** > **Environment Variables**
4. Enter **Name** and **Value** for each environment variable in:

   - [API - Environment Variables - Convex ↗][api-convex]

#### 2. Generate Convex Deployment Key

1. Navigate to the [Convex Dashboard][convex-dashboard]
2. Select project
3. Navigate to **Settings** > **URL & Deploy Key** > **Show development credentials**
4. Select **Generate Deploy Key**
   - **Deploy Key Name:** `GitHub Actions`

#### 3. Set GitHub Environment Variables

1. Navigate to the [RackUp GitHub Repository][github-rackup]
2. Navigate to **Settings** > **Environments**
3. Select **New environment**
   - **Name:** e.g. `Development`
4. Select **Add environment secret**

   - Enter **Name** and **Value** for each environment variable with **Exposure** of ![secret][image-type-secret] in:

     - [API - Environment Variables - GitHub Actions ↗][api-github-actions]

5. Select **Add environment variable**

   - Enter **Name** and **Value** for each environment variable with **Exposure** of ![variable][image-type-variable] in:

     - [API - Environment Variables - GitHub Actions ↗][api-github-actions]

[api-convex]: ../../packages/api/README.md#convex
[api-github-actions]: ../../packages/api/README.md#github-actions
[convex-dashboard]: https://dashboard.convex.dev
[github-rackup]: https://github.com/hypernova-engineering/rackup
[home]: ../../README.md
[image-type-secret]: ../images/types/secret.svg
[image-type-variable]: ../images/types/variable.svg
