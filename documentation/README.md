# :notebook: Documentation

[⏎ Home][home]

<!-- markdownlint-disable link-image-style -->

- [Copy](#copy)
  - [Labels](#labels)
- [Diagrams](#diagrams)
  - [Export](#export)
- [Images](#images)
  - [Types](#types)

<!-- markdownlint-enable link-image-style -->

## Copy

### Labels

Denote labels the reader is expected to interact with using **bold** text.

```markdown
1. Navigate to **Settings** > **Environments**
2. Select **New environment**
```

## Diagrams

[Excalidraw ↗][excalidraw]

### Export

| Name                            | Background      | Dark mode          | Embed scene        | Scale |
| ------------------------------- | --------------- | ------------------ | ------------------ | ----- |
| `<NAME>-diagram.png`            | :no_entry_sign: | :no_entry_sign:    | :white_check_mark: | 1x    |
| `<NAME>-diagram-dark-mode.svg`  | :no_entry_sign: | :white_check_mark: | :white_check_mark: | 1x    |
| `<NAME>-diagram-light-mode.svg` | :no_entry_sign: | :no_entry_sign:    | :white_check_mark: | 1x    |

## Images

### Types

[Badgen ↗][badgen]

| Name       | Image                            |
| ---------- | -------------------------------- |
| `boolean`  | ![string][image-type-boolean]    |
| `secret`   | ![secret][image-type-secret]     |
| `string`   | ![string][image-type-string]     |
| `variable` | ![variable][image-type-variable] |

[badgen]: https://badgen.net/badge/color/NAME/black?label=
[excalidraw]: https://excalidraw.com
[home]: ../README.md
[image-type-boolean]: ./images/types/boolean.svg
[image-type-secret]: ./images/types/secret.svg
[image-type-string]: ./images/types/string.svg
[image-type-variable]: ./images/types/variable.svg
