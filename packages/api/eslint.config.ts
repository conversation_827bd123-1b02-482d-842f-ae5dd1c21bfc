import eslintJs from "@eslint/js";
import eslintConfigPrettier from "eslint-config-prettier/flat";
import typescriptEslint from "typescript-eslint";

const config = typescriptEslint.config(
  {
    extends: [
      eslintJs.configs.recommended,
      typescriptEslint.configs.recommended,
    ],
    files: ["src/**/*.ts"],
  },
  {
    ignores: ["src/_generated/**"],
  },
  /**
   * Prettier must be last
   * @see https://github.com/prettier/eslint-config-prettier#installation
   */
  eslintConfigPrettier,
);

export default config;
