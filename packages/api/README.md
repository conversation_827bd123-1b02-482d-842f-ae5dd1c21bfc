# :zap: API

[⏎ Home][home]

<!-- markdownlint-disable link-image-style -->

- [Environment Variables](#environment-variables)
  - [Local](#local)
  - [GitHub Actions](#github-actions)
  - [Convex](#convex)

<!-- markdownlint-enable link-image-style -->

## Environment Variables

### Local

| Name                | Type                         | Description                 |
| ------------------- | ---------------------------- | --------------------------- |
| `CONVEX_DEPLOYMENT` | ![string][image-type-string] | Development deployment name |
| `CONVEX_URL`        | ![string][image-type-string] | Deployment URL              |

### GitHub Actions

| Name                | Type                         | Exposure                     | Description    |
| ------------------- | ---------------------------- | ---------------------------- | -------------- |
| `CONVEX_DEPLOY_KEY` | ![string][image-type-string] | ![secret][image-type-secret] | Deployment key |

### Convex

| Name              | Type                         | Description                                                                                                                                                    |
| ----------------- | ---------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `JWKS`            | ![string][image-type-string] | JSON Web Key Set. Generate using [Convex Auth - Manual Setup - Configure private and public key ↗][convex-auth-manual-setup-configure-private-and-public-key] |
| `JWT_PRIVATE_KEY` | ![string][image-type-string] | JWT private key. Generate using [Convex Auth - Manual Setup - Configure private and public key ↗][convex-auth-manual-setup-configure-private-and-public-key]  |

[convex-auth-manual-setup-configure-private-and-public-key]: https://labs.convex.dev/auth/setup/manual#configure-private-and-public-key
[home]: ../../README.md
[image-type-secret]: ../../documentation/images/types/secret.svg
[image-type-string]: ../../documentation/images/types/string.svg
