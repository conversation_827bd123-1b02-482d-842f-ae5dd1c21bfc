import { GenericQueryCtx } from "convex/server";
import { ConvexError, v } from "convex/values";

import { DataModel, Id } from "./_generated/dataModel";
import { mutation, query } from "./_generated/server";
import { getUserId } from "./getUserId";

const throwNotFoundError = (model: string) => {
  throw new ConvexError({
    code: "NOT_FOUND",
    message: `${model} does not exist`,
  });
};

const throwAlreadyExistsError = (model: string) => {
  throw new ConvexError({
    code: "ALREADY_EXISTS",
    message: `${model} with that name already exists`,
  });
};

const throwOnAlreadyExists = async (
  ctx: GenericQueryCtx<DataModel>,
  model: "workouts",
  name: string,
  userId: Id<"users">,
) => {
  const workout = await ctx.db
    .query(model)
    .withIndex("byNameUser", (query) =>
      query.eq("name", name).eq("user", userId),
    )
    .unique();

  if (workout) throwAlreadyExistsError("Workout");
};

const getByName = async (
  ctx: GenericQueryCtx<DataModel>,
  name: string,
  userId: Id<"users">,
) => {
    const workout = await ctx.db
    .query(model)
    .withIndex("byNameUser", (query) =>
      query.eq("name", name).eq("user", userId),
    )
    .unique();
    
}

const getById = async (
  ctx: GenericQueryCtx<DataModel>,
  id: Id<"workouts">,
  userId: Id<"users">,
) => {
  const workout = await ctx.db.get(id);
  if (!workout) throwNotFoundError("Workout");
  if (workout.user !== userId) throwNotFoundError("Workout");

  return workout;
};

const create = mutation({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getUserId(ctx);
    throwOnAlreadyExists(ctx, "workouts", args.name, userId);

    await ctx.db.insert("workouts", {
      name: args.name,
      user: userId,
    });
  },
});

const get = query({
  args: {
    id: v.id("workouts"),
  },
  handler: async (ctx, args) => {
    const userId = await getUserId(ctx);
    const workout = await getById(ctx, args.id, userId);

    return workout;
  },
});

const getAll = query({
  handler: async (ctx) => {
    const userId = await getUserId(ctx);

    const workouts = await ctx.db
      .query("workouts")
      .withIndex("byUser", (query) => query.eq("user", userId))
      .collect();

    return workouts;
  },
});

const edit = mutation({
  args: {
    id: v.id("workouts"),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getUserId(ctx);
    throwOnAlreadyExists(ctx, "workouts", args.name, userId);

    await ctx.db.patch(args.id, {
      name: args.name,
    });
  },
});

const remove = mutation({
  args: {
    id: v.id("workouts"),
  },
  handler: async (ctx, args) => {
    const userId = await getUserId(ctx);
    await getById(ctx, args.id, userId);

    await ctx.db.delete(args.id);
  },
});

export { create, edit, get, getAll, remove };
