import { getAuthUserId } from "@convex-dev/auth/server";
import { GenericQueryCtx } from "convex/server";
import { ConvexError } from "convex/values";

import { DataModel } from "./_generated/dataModel";

const getUserId = async (ctx: GenericQueryCtx<DataModel>) => {
  const userId = await getAuthUserId(ctx);

  if (userId === null)
    throw new ConvexError({
      code: "UNAUTHORIZED",
      message: "Unauthorized",
    });

  return userId;
};

export { getUserId };
