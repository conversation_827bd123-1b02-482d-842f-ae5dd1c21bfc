/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as getUserId from "../getUserId.js";
import type * as http from "../http.js";
import type * as seeds_admin from "../seeds/admin.js";
import type * as seeds_exercises from "../seeds/exercises.js";
import type * as seeds from "../seeds.js";
import type * as workouts from "../workouts.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  getUserId: typeof getUserId;
  http: typeof http;
  "seeds/admin": typeof seeds_admin;
  "seeds/exercises": typeof seeds_exercises;
  seeds: typeof seeds;
  workouts: typeof workouts;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
