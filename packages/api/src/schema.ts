import { authTables } from "@convex-dev/auth/server";
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

const schema = defineSchema(
  {
    ...authTables,
    exercises: defineTable({
      name: v.string(),
    }).index("name", ["name"]),
    userExercises: defineTable({
      name: v.string(),
    }),
    workouts: defineTable({
      exercises: v.optional(
        v.array(v.union(v.id("exercises"), v.id("userExercises"))),
      ),
      name: v.string(),
      user: v.id("users"),
    })
      .index("byUser", ["user"])
      .index("byNameUser", ["name", "user"]),
  },
  { schemaValidation: true },
);

export default schema;
