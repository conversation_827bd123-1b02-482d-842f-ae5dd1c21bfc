import { internalMutation } from "../_generated/server";

const exercises = internalMutation({
  handler: async (ctx) => {
    const names = [
      "21's",
      "Face Pull",
      "Lat Pulldown",
      "Seated Row",
      "Tricep Kickbacks",
      "Weighted Dips",
    ];

    names.forEach(async (name) => {
      const exercise = await ctx.db
        .query("exercises")
        .withIndex("name", (query) => query.eq("name", name))
        .unique();

      if (!exercise) await ctx.db.insert("exercises", { name });
    });
  },
});

export { exercises };
