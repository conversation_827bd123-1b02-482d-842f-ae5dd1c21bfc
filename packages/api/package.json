{"name": "@rackup/api", "version": "1.0.0-alpha.0", "private": true, "license": "UNLICENSED", "scripts": {"format:check": "prettier . --check", "format:fix": "prettier . --write", "lint": "tsc -p convex && eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:check": "eslint", "lint:fix": "eslint --fix", "logs": "convex logs", "seed": "convex run seeds:run", "prestart": "convex dev --until-success && convex dashboard", "start": "convex dev", "type:check": "tsc --noEmit"}, "dependencies": {"@auth/core": "0.37.0", "@convex-dev/auth": "0.0.88", "convex": "1.26.2"}, "devDependencies": {"@eslint/js": "9.34.0", "@ianvs/prettier-plugin-sort-imports": "4.7.0", "@types/node": "^22", "eslint": "9.34.0", "eslint-config-prettier": "10.1.8", "prettier": "3.6.2", "prettier-plugin-packagejson": "2.5.19", "typescript": "5.9.2", "typescript-eslint": "8.42.0"}, "packageManager": "pnpm@10.15.0", "engines": {"node": "^22"}}