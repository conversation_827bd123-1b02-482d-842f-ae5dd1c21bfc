{"name": "@rackup/mobile", "version": "1.0.0-alpha.0", "private": true, "license": "UNLICENSED", "main": "expo-router/entry", "scripts": {"format:check": "prettier . --check", "format:fix": "prettier . --write", "lint": "expo lint", "lint:check": "pnpm run lint", "start": "expo start", "start:android": "expo start --android", "start:ios": "expo start --ios", "start:web": "expo start --web", "type:check": "echo 'Not implemented'"}, "dependencies": {"@auth/core": "0.37.0", "@convex-dev/auth": "0.0.88", "@expo/vector-icons": "14.1.0", "@hookform/resolvers": "5.2.1", "@rackup/api": "workspace:*", "@react-navigation/bottom-tabs": "7.4.7", "@react-navigation/elements": "2.6.4", "@react-navigation/native": "7.1.17", "@rn-primitives/dialog": "1.2.0", "@rn-primitives/label": "1.2.0", "@rn-primitives/portal": "1.3.0", "@rn-primitives/separator": "1.2.0", "@rn-primitives/slot": "1.2.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "expo": "53.0.22", "expo-blur": "14.1.5", "expo-constants": "17.1.7", "expo-font": "13.3.2", "expo-haptics": "14.1.4", "expo-image": "2.4.0", "expo-linking": "7.1.7", "expo-router": "5.1.6", "expo-secure-store": "14.2.4", "expo-splash-screen": "0.30.10", "expo-status-bar": "2.2.3", "expo-symbols": "0.4.5", "expo-system-ui": "5.0.11", "expo-web-browser": "14.2.0", "lucide-react-native": "0.543.0", "nativewind": "4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.62.0", "react-native": "0.79.5", "react-native-gesture-handler": "2.24.0", "react-native-reanimated": "3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.11.1", "react-native-web": "0.20.0", "react-native-webview": "13.13.5", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "zod": "4.1.5"}, "devDependencies": {"@babel/core": "7.28.4", "@ianvs/prettier-plugin-sort-imports": "4.7.0", "@types/react": "19.0.10", "eslint": "9.35.0", "eslint-config-expo": "9.2.0", "prettier": "3.6.2", "prettier-plugin-packagejson": "2.5.19", "prettier-plugin-tailwindcss": "0.6.14", "tailwindcss": "3.4.17", "typescript": "5.8.3"}}