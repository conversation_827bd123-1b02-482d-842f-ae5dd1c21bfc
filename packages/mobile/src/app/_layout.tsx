import "./global.css";

import { PortalHost } from "@rn-primitives/portal";
import { useConvexAuth } from "convex/react";
import { Stack } from "expo-router";

import { ConvexProvider } from "@/providers";

const RootLayout = () => (
  <ConvexProvider>
    <Layout />
    <PortalHost />
  </ConvexProvider>
);

const Layout = () => {
  const { isAuthenticated } = useConvexAuth();

  return (
    <Stack>
      <Stack.Protected guard={isAuthenticated}>
        <Stack.Screen name="(app)" />
      </Stack.Protected>
      <Stack.Protected guard={!isAuthenticated}>
        <Stack.Screen name="index" />
      </Stack.Protected>
    </Stack>
  );
};

export default RootLayout;
