import { api } from "@rackup/api/src/_generated/api";
import { Id } from "@rackup/api/src/_generated/dataModel";
import { useQuery } from "convex/react";
import { useLocalSearchParams } from "expo-router";
import { View } from "react-native";

import { Text } from "@/components/ui/text";

const Page = () => {
  const local = useLocalSearchParams<{ id: Id<"workouts"> }>();
  const workout = useQuery(api.workouts.get, { id: local.id });

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Text variant="h1">{workout?.name}</Text>;
    </View>
  );
};

export default Page;
