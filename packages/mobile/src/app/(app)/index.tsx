import { useAuthActions } from "@convex-dev/auth/react";
import { api } from "@rackup/api/src/_generated/api";
import { Id } from "@rackup/api/src/_generated/dataModel";
import { useMutation, useQuery } from "convex/react";
import { Link } from "expo-router";
import { Trash } from "lucide-react-native";
import { View } from "react-native";

import { NewWorkoutDialog } from "@/components/dialogs/NewWorkoutDialog";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Icon } from "@/components/ui/icon";
import { Separator } from "@/components/ui/separator";
import { Text } from "@/components/ui/text";

const Index = () => {
  const workouts = useQuery(api.workouts.getAll);
  const removeWorkout = useMutation(api.workouts.remove);
  const { signOut } = useAuthActions();

  const onDeleteWorkout = async (id: Id<"workouts">) => {
    await removeWorkout({ id });
  };

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Text variant="h1">Workouts ({workouts?.length})</Text>
      {workouts &&
        workouts.map((workout) => (
          <Card className="w-full max-w-sm" key={workout._id}>
            <CardHeader className="flex-row justify-between">
              <CardTitle>
                <Link href={`/(app)/workout/${workout._id.toString()}`}>
                  {workout.name}
                </Link>
              </CardTitle>
              <Icon
                color={"red"}
                onPress={() => onDeleteWorkout(workout._id)}
                as={Trash}
              />
            </CardHeader>
          </Card>
        ))}
      <Separator />
      <NewWorkoutDialog />
      <Separator />
      <Button onPress={signOut}>
        <Text>Logout</Text>
      </Button>
    </View>
  );
};

export default Index;
