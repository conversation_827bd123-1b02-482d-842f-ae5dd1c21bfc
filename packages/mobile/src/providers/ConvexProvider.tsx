import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ConvexReactClient } from "convex/react";
import { deleteItemAsync, getItemAsync, setItemAsync } from "expo-secure-store";
import { ReactNode } from "react";

type Props = {
  children: ReactNode;
};

const client = new ConvexReactClient(process.env.EXPO_PUBLIC_CONVEX_URL!, {
  unsavedChangesWarning: false,
});

const storage = {
  getItem: getItemAsync,
  removeItem: deleteItemAsync,
  setItem: setItemAsync,
};

const ConvexProvider = ({ children }: Props) => (
  <ConvexAuthProvider client={client} storage={storage}>
    {children}
  </ConvexAuthProvider>
);

export { ConvexProvider };
