import { useAuthActions } from "@convex-dev/auth/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { View } from "react-native";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Text } from "@/components/ui/text";

type Values = z.infer<typeof schema>;

const schema = z.object({
  email: z.string().min(1),
  password: z.string().min(1),
});

const Login = () => {
  const { control, handleSubmit } = useForm({
    defaultValues: { email: "", password: "" },
    resolver: zodResolver(schema),
  });
  const { signIn } = useAuthActions();

  const onSubmit = async (values: Values) => {
    await signIn("password", {
      flow: "signIn",
      email: values.email,
      password: values.password,
    });
  };

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Text variant="h1">Login</Text>
      <Controller
        name="email"
        control={control}
        render={({ field: { onBlur, onChange, value } }) => (
          <Input
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            placeholder="Email"
          />
        )}
      />
      <Controller
        name="password"
        control={control}
        render={({ field: { onBlur, onChange, value } }) => (
          <Input
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            placeholder="Password"
            secureTextEntry
          />
        )}
      />
      <Button onPress={handleSubmit(onSubmit)}>
        <Text>Login</Text>
      </Button>
    </View>
  );
};

export { Login };
