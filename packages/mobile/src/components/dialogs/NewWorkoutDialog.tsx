import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { api } from "@rackup/api/src/_generated/api";
import { useMutation } from "convex/react";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { View } from "react-native";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Text } from "@/components/ui/text";

type Values = z.infer<typeof schema>;

const schema = z.object({
  name: z.string().min(1),
});

const NewWorkoutDialog = () => {
  const [open, setOpen] = useState(false);
  const { control, handleSubmit } = useForm({
    defaultValues: { name: "" },
    resolver: zodResolver(schema),
  });
  const createWorkout = useMutation(api.workouts.create);

  const onSubmit = async (values: Values) => {
    await createWorkout({ name: values.name });

    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Text>New Workout</Text>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>New Workout</DialogTitle>
          <DialogDescription>
            Create a new workout. Click create when you&apos;re done.
          </DialogDescription>
        </DialogHeader>
        <View className="grid gap-4">
          <View className="grid gap-3">
            <Label htmlFor="name">Name</Label>
            <Controller
              name="name"
              control={control}
              render={({ field: { onBlur, onChange, value } }) => (
                <Input onBlur={onBlur} onChangeText={onChange} value={value} />
              )}
            />
          </View>
        </View>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">
              <Text>Cancel</Text>
            </Button>
          </DialogClose>
          <Button onPress={handleSubmit(onSubmit)}>
            <Text>Create</Text>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export { NewWorkoutDialog };
